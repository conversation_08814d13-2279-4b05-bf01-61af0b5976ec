package vip.xiaonuo.bugly.modular.query.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import vip.xiaonuo.bugly.modular.monitor.entity.MonitorBuglyHour;
import vip.xiaonuo.bugly.modular.query.service.HallQueryService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 大厅查询控制器测试类
 *
 * <AUTHOR>
 * @date 2025/08/08 15:45
 */
@WebMvcTest(HallQueryController.class)
public class HallQueryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HallQueryService hallQueryService;

    @Test
    public void testListExceptionDataMonitor() throws Exception {
        // 准备测试数据
        List<MonitorBuglyHour> mockResult = new ArrayList<>();
        MonitorBuglyHour mockData = new MonitorBuglyHour();
        mockData.setId("test-id");
        mockData.setAppId("test-app");
        mockData.setDay("2025-08-08");
        mockData.setHour(15);
        mockResult.add(mockData);

        // Mock service方法
        when(hallQueryService.listExceptionDataMonitor(
                anyString(), anyString(), anyList(), anyList(), anyList()))
                .thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/bugly/query/hallQuery/listExceptionDataMonitor")
                        .param("dayHour", "2025-08-08 15:00:00")
                        .param("dayOrHour", "hour")
                        .param("appIds", "app1,app2")
                        .param("curGameIds", "game1,game2")
                        .param("crashModules", "module1,module2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value("test-id"))
                .andExpect(jsonPath("$.data[0].appId").value("test-app"));
    }

    @Test
    public void testListHeartbeatDataMonitor() throws Exception {
        // 准备测试数据
        List<MonitorBuglyHour> mockResult = new ArrayList<>();
        MonitorBuglyHour mockData = new MonitorBuglyHour();
        mockData.setId("test-heartbeat-id");
        mockData.setAppId("test-heartbeat-app");
        mockData.setDay("2025-08-08");
        mockData.setHour(15);
        mockData.setCntHeartbeatUser(100L);
        mockResult.add(mockData);

        // Mock service方法
        when(hallQueryService.listHeartbeatDataMonitor(
                anyString(), anyString(), anyList()))
                .thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/bugly/query/hallQuery/listHeartbeatDataMonitor")
                        .param("dayHour", "2025-08-08 15:00:00")
                        .param("dayOrHour", "hour")
                        .param("appIds", "app1,app2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value("test-heartbeat-id"))
                .andExpect(jsonPath("$.data[0].appId").value("test-heartbeat-app"))
                .andExpect(jsonPath("$.data[0].cntHeartbeatUser").value(100));
    }

    @Test
    public void testListExceptionDataMonitorWithEmptyParams() throws Exception {
        // 准备测试数据
        List<MonitorBuglyHour> mockResult = new ArrayList<>();

        // Mock service方法
        when(hallQueryService.listExceptionDataMonitor(
                isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(mockResult);

        // 执行测试 - 不传任何参数
        mockMvc.perform(get("/bugly/query/hallQuery/listExceptionDataMonitor"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    public void testListHeartbeatDataMonitorWithEmptyParams() throws Exception {
        // 准备测试数据
        List<MonitorBuglyHour> mockResult = new ArrayList<>();

        // Mock service方法
        when(hallQueryService.listHeartbeatDataMonitor(
                isNull(), isNull(), isNull()))
                .thenReturn(mockResult);

        // 执行测试 - 不传任何参数
        mockMvc.perform(get("/bugly/query/hallQuery/listHeartbeatDataMonitor"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data").isEmpty());
    }
}
