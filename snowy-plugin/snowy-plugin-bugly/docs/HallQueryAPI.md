# 大厅查询API接口文档

## 概述

大厅查询控制器提供了两个主要的数据查询接口，用于获取异常数据和心跳数据的监控信息。

## 接口列表

### 1. 查询异常数据接口

**接口地址：** `GET /bugly/query/hallQuery/listExceptionDataMonitor`

**接口描述：** 根据时间查询异常数据(天或小时)(汇总数据)(<PERSON>)

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| dayHour | String | 否 | 时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss |
| dayOrHour | String | 否 | =day按天统计，=hour 按小时统计，默认小时 |
| appIds | String | 否 | 应用ID列表，多个用逗号分隔，如：app1,app2,app3 |
| curGameIds | String | 否 | cur_gameid列表，多个用逗号分隔，如：game1,game2,game3 |
| crashModules | String | 否 | crashModule异常模块列表，多个用逗号分隔，如：module1,module2,module3 |

**请求示例：**
```
GET /bugly/query/hallQuery/listExceptionDataMonitor?dayHour=2025-08-08 15:00:00&dayOrHour=hour&appIds=app1,app2&curGameIds=game1,game2&crashModules=module1,module2
```

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "hour_total_2025-08-08_15_app1",
      "day": "2025-08-08",
      "hour": 15,
      "appId": "app1",
      "appPlatform": 1,
      "appVersion": "1.0.0",
      "appChannel": "official",
      "cntDataTotal": 100,
      "cntUserTotal": 50,
      "cntDataCrash": 10,
      "cntDataCaton": 5,
      "cntDataError": 15,
      "cntDataReport": 20,
      "cntDataAnr": 3,
      "cntUserCrash": 8,
      "cntUserCaton": 4,
      "cntUserError": 12,
      "cntUserReport": 15,
      "cntUserAnr": 2,
      "timeType": "hour",
      "dataType": "total",
      "dt": "2025-08-08T15:00:00"
    }
  ]
}
```

### 2. 查询心跳数据接口

**接口地址：** `GET /bugly/query/hallQuery/listHeartbeatDataMonitor`

**接口描述：** 根据时间查询心跳数据(天或小时)(Doris)

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| dayHour | String | 否 | 时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss |
| dayOrHour | String | 否 | =day按天统计，=hour 按小时统计，默认小时 |
| appIds | String | 否 | 应用ID列表，多个用逗号分隔，如：app1,app2,app3 |

**请求示例：**
```
GET /bugly/query/hallQuery/listHeartbeatDataMonitor?dayHour=2025-08-08 15:00:00&dayOrHour=hour&appIds=app1,app2
```

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": "hour_total_2025-08-08_15_app1",
      "day": "2025-08-08",
      "hour": 15,
      "appId": "app1",
      "appPlatform": 1,
      "appVersion": "1.0.0",
      "appChannel": "official",
      "cntHeartbeatLaunch": 1000,
      "cntHeartbeatUser": 500,
      "timeType": "hour",
      "dataType": "total",
      "dt": "2025-08-08T15:00:00"
    }
  ]
}
```

## 响应状态码

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 500 | 服务器内部错误 |

## 注意事项

1. **时间格式：** dayHour参数需要使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **默认值：** 
   - dayOrHour默认为"hour"
   - 如果不传dayHour参数，系统会自动计算默认时间
3. **参数分隔：** 多个ID使用英文逗号分隔，不要有空格
4. **数据来源：** 数据来自Doris数据库
5. **权限：** 需要相应的接口访问权限

## 使用场景

1. **异常监控：** 通过异常数据接口可以监控应用的崩溃、卡顿、错误等异常情况
2. **用户活跃度：** 通过心跳数据接口可以了解应用的启动次数和用户数量
3. **数据分析：** 支持按天或按小时的粒度进行数据统计分析
4. **多应用监控：** 支持同时查询多个应用的数据

## 错误处理

接口内部已经包含了异常处理机制，如果查询失败会返回空列表而不是抛出异常，确保接口的稳定性。

## 性能建议

1. 建议根据实际需要选择合适的时间粒度（天或小时）
2. 避免一次性查询过多的应用ID
3. 合理设置查询时间范围，避免查询过长时间段的数据
