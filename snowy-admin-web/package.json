{"name": "snowy-admin-web", "version": "1.0.0", "private": true, "description": "小诺团队旗下Snowy前端，基于Antdv3.2+Vue3.2+Vite2.8", "repository": {"type": "git", "url": "https://www.gitee.com/xiaonuobase/snowy"}, "license": "Apache-2.0", "author": "<PERSON><PERSON><PERSON><PERSON>", "scripts": {"serve": "vite", "dev": "vite --mode development", "preview": "vite preview", "build": "vite build --mode production", "prod": "vite  --mode production"}, "dependencies": {"@ant-design/colors": "6.0.0", "@ant-design/icons-vue": "^6.1.0", "@antv/g2plot": "2.4.10", "@chenfengyuan/vue-qrcode": "2.0.0", "@highlightjs/vue-plugin": "^2.1.0", "@tinymce/tinymce-vue": "5.0.0", "amis": "^6.3.0", "ant-design-vue": "3.2.14", "axios": "1.1.3", "cropperjs": "1.5.12", "dayjs": "^1.11.6", "echarts": "^5.4.0", "echarts-stat": "^1.2.0", "enquire.js": "^2.1.6", "fuse.js": "^6.6.2", "highlight.js": "^11.6.0", "hotkeys-js": "^3.10.0", "js-pinyin": "^0.1.9", "lodash-es": "^4.17.21", "nprogress": "0.2.0", "screenfull": "^6.0.2", "sm-crypto": "^0.3.11", "snowflake-id": "^1.1.0", "snowy-form-design": "^1.1.9-Bata-02", "sortablejs": "^1.15.0", "tinymce": "6.2.0", "vue": "3.2.44", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.0.1", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue3-colorpicker": "2.0.4", "vue3-tree-org": "^4.1.1", "vuedraggable-es": "^4.1.1", "vuex": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "^0.29.3", "@babel/eslint-parser": "^7.19.1", "@vitejs/plugin-legacy": "^1.6.4", "@vitejs/plugin-vue": "^2.1.0", "@vitejs/plugin-vue-jsx": "^1.3.8", "@vue/compiler-sfc": "^3.2.41", "@vue/eslint-config-standard": "^8.0.1", "antd-less-to-css-variable": "^1.0.5", "autoprefixer": "^10.4.13", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.7.0", "less": "^4.1.3", "postcss": "^8.4.18", "prettier": "^2.7.1", "rollup-plugin-visualizer": "^5.8.3", "tailwindcss": "^3.2.1", "typescript": "^4.8.4", "unplugin-auto-import": "^0.11.4", "unplugin-vue-components": "^0.22.9", "vite": "2.8.6", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}