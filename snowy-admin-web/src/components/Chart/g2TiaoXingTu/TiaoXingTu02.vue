<template>
	<div id="TiaoXingTu02"></div>
</template>

<script setup>
	import { onMounted } from 'vue'
	import { Bar } from '@antv/g2plot'

	const data = [
		{
			year: '1991',
			value: 3,
			type: 'Lon'
		},
		{
			year: '1992',
			value: 4,
			type: 'Lon'
		},
		{
			year: '1993',
			value: 3.5,
			type: 'Lon'
		},
		{
			year: '1994',
			value: 5,
			type: 'Lon'
		},
		{
			year: '1995',
			value: 4.9,
			type: 'Lon'
		},
		{
			year: '1996',
			value: 6,
			type: 'Lon'
		},
		{
			year: '1997',
			value: 7,
			type: 'Lon'
		},
		{
			year: '1998',
			value: 9,
			type: 'Lon'
		},
		{
			year: '1999',
			value: 13,
			type: 'Lon'
		},
		{
			year: '1991',
			value: 3,
			type: 'Bor'
		},
		{
			year: '1992',
			value: 4,
			type: 'Bor'
		},
		{
			year: '1993',
			value: 3.5,
			type: 'Bor'
		},
		{
			year: '1994',
			value: 5,
			type: 'Bor'
		},
		{
			year: '1995',
			value: 4.9,
			type: 'Bor'
		},
		{
			year: '1996',
			value: 6,
			type: 'Bor'
		},
		{
			year: '1997',
			value: 7,
			type: 'Bor'
		},
		{
			year: '1998',
			value: 9,
			type: 'Bor'
		},
		{
			year: '1999',
			value: 13,
			type: 'Bor'
		}
	]

	onMounted(() => {
		const stackedBarPlot = new Bar('TiaoXingTu02', {
			data: data.reverse(),
			isStack: true,
			xField: 'value',
			yField: 'year',
			seriesField: 'type',
			label: {
				// 可手动配置 label 数据标签位置
				position: 'middle', // 'left', 'middle', 'right'
				// 可配置附加的布局方法
				layout: [
					// 柱形图数据标签位置自动调整
					{ type: 'interval-adjust-position' },
					// 数据标签防遮挡
					{ type: 'interval-hide-overlap' },
					// 数据标签文颜色自动调整
					{ type: 'adjust-color' }
				]
			}
		})

		stackedBarPlot.render()
	})
</script>

<style scoped></style>
