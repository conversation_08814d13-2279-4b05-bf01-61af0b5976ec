<template>
  <div ref="amisRef"></div>
</template>

<script setup name="XnAmis">
	import { onMounted } from 'vue';
	import 'amis/sdk/sdk.css';
	import 'amis/sdk/helper.css';
	import 'amis/sdk/iconfont.css';
	import 'amis/sdk/sdk.js';
    const amisRef = ref();
    let amisScoped;

	const props = defineProps({
		amisJSON: {
			type: Object,
			default: () => ({})
		}
	})

        // 通过替换下面这个配置来生成不同页面
        let amisJSONDemo = {
          type: 'page',
          title: '表单页面',
          body: {
            type: 'form',
            mode: 'horizontal',
            api: '/saveForm',
            body: [
              {
                label: 'Name',
                type: 'input-text',
                name: 'name'
              },
              {
                label: 'Email',
                type: 'input-email',
                name: 'email'
              }
            ]
          }
        };

	onMounted(() => {
    	let amis = amisRequire('amis/embed');
    	amisScoped = amis.embed(amisRef.value, props.amisJSON);
	})

    // 获取数据
    const getValues = (id) => {
         return amisScoped.getComponentById(id).getValues();
    }
    // 设置数据
    const setValues = (id,data) => {
         amisScoped.getComponentById(id).setValues(data);
    }
    // 抛出函数
    defineExpose({
        getValues,
        setValues
    })
</script>
