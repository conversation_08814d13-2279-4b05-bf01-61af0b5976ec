<!DOCTYPE html>
<html lang="zh_cn">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="/favicon.ico">
  <title>Snowy</title>
  <style>
    .dot{animation:antRotate 1.2s infinite linear;transform:rotate(45deg);position:relative;display:inline-block;font-size:32px;width:32px;height:32px;box-sizing:border-box}.dot i{width:14px;height:14px;position:absolute;display:block;background-color:#1890ff;border-radius:100%;transform:scale(.75);transform-origin:50% 50%;opacity:.3;animation:antSpinMove 1s infinite linear alternate}.dot i:nth-child(1){top:0;left:0}.dot i:nth-child(2){top:0;right:0;-webkit-animation-delay:.4s;animation-delay:.4s}.dot i:nth-child(3){right:0;bottom:0;-webkit-animation-delay:.8s;animation-delay:.8s}.dot i:nth-child(4){bottom:0;left:0;-webkit-animation-delay:1.2s;animation-delay:1.2s}@keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@-webkit-keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@keyframes antSpinMove{to{opacity:1}}@-webkit-keyframes antSpinMove{to{opacity:1}}
    .app-loading {position: absolute;top:0px;left:0px;right:0px;bottom:0px;display: flex;justify-content: center;align-items: center;flex-direction: column;background: #fff;}
    .app-loading__logo {margin-bottom: 30px;}
    .app-loading__logo img {width: 90px;vertical-align: bottom;}
    .app-loading__title {font-size: 24px;color: #333;margin-top: 30px;}
    @keyframes loader {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  </style>

</head>
<body>
<noscript>
  <strong>We're sorry but Snowy2.0 doesn't work properly without JavaScript
    enabled. Please enable it to continue.</strong>
</noscript>
<div id="app" class="aminui">
  <div class="app-loading">
    <div class="app-loading__logo">
      <img src="./img/logo.png"/>
    </div>
    <div><span class="dot dot-spin"><i></i><i></i><i></i><i></i></span></div>
    <div class="app-loading__title">Snowy</div>
  </div>
</div>
<script type="module" src="./src/main.js"></script>
</body>
</html>
